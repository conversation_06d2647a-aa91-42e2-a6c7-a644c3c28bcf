<template>
    <Dialog
        v-if="visible"
        title="Import Details"
        :width="800"
        @close="closeModal"
        :class="'k-modal-window'"
        :dialog-class="'tw-dialog custom-modal-wrapper'"
        append-to="body"
    >
        <div v-if="importData" class="space-y-4 p-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <h3 class="text-sm font-semibold text-gray-500">Student ID</h3>
                    <p>{{ getStudentId }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500">Unit ID</h3>
                    <p>{{ getUnitId }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500">Course Type</h3>
                    <p>{{ getCourseType }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500">Total Mark</h3>
                    <p>{{ getTotal }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500">Status</h3>
                    <Badge :variant="getStatusVariant">{{ importData.status }}</Badge>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500">Imported At</h3>
                    <p>{{ formatDate }}</p>
                </div>
            </div>
            <div v-if="Object.keys(getAssessmentItems).length > 0">
                <h3 class="mb-2 text-sm font-semibold text-gray-500">Assessment Items</h3>
                <div class="rounded-md bg-gray-50 p-4">
                    <div
                        v-for="(value, key) in getAssessmentItems"
                        :key="key"
                        class="flex justify-between"
                    >
                        <span>{{ key }}</span>
                        <span>{{ value }}</span>
                    </div>
                </div>
            </div>
            <div v-if="importData.status === 'failed' && importData.error_message">
                <h3 class="mb-2 text-sm font-semibold text-gray-500">Error Details</h3>
                <div class="rounded-md bg-red-50 p-4">
                    <ul class="list-disc pl-5 text-red-700">
                        <li v-for="(error, index) in parseErrors" :key="index">
                            <strong>{{ error.error_title }}:</strong> {{ error.error_description }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <DialogActionsBar>
            <div class="flex justify-end space-x-2">
                <Button variant="secondary" @click="closeModal"> Close </Button>
                <Button
                    v-if="importData && importData.status === 'failed'"
                    variant="primary"
                    @click="resyncImport"
                >
                    Retry Import
                </Button>
            </div>
        </DialogActionsBar>
    </Dialog>
</template>

<script>
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import Badge from '@spa/components/badges/Badge.vue';

export default {
    components: {
        Dialog,
        DialogActionsBar,
        Button,
        Badge,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        importData: {
            type: Object,
            default: null,
        },
    },
    computed: {
        getStudentId() {
            return this.importData?.import_data?.StudentId || 'N/A';
        },
        getUnitId() {
            return this.importData?.import_data?.UnitId || 'N/A';
        },
        getCourseType() {
            return this.importData?.import_data?.CourseType || 'N/A';
        },
        getTotal() {
            return this.importData?.import_data?.TotalMark || 'N/A';
        },
        getAssessmentItems() {
            return this.importData?.import_data?.assessmentData || {};
        },
        getStatusVariant() {
            switch (this.importData?.status) {
                case 'completed':
                    return 'success';
                case 'pending':
                    return 'info';
                case 'failed':
                    return 'error';
                default:
                    return 'secondary';
            }
        },
        formatDate() {
            if (!this.importData?.created_at) return 'N/A';
            const date = new Date(this.importData.created_at);
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
            });
        },
        parseErrors() {
            try {
                return JSON.parse(this.importData?.error_message || '[]');
            } catch (e) {
                return [
                    {
                        error_title: 'Error',
                        error_description: this.importData?.error_message || 'Unknown error',
                    },
                ];
            }
        },
    },
    methods: {
        closeModal() {
            this.$emit('update:visible', false);
        },
        resyncImport() {
            this.$emit('resync', this.importData);
            this.closeModal();
        },
    },
};
</script>
