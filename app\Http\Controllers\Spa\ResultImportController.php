<?php

namespace App\Http\Controllers\Spa;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Jobs\ProcessResultImport;
use App\Model\v2\ResultImport;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ResultImportController extends Controller
{
    use ResponseTrait;

    /**
     * Display the result import page.
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        return Inertia::render('result-import/ResultImportMain');
    }

    /**
     * Get paginated result imports.
     *
     * @return \Inertia\Response
     */
    public function getImports(Request $request)
    {
        $sortDirection = $request->input('dir', 'desc');
        $sortBy = $request->input('sortBy', 'created_at');
        $perPage = $request->input('take', 10);
        $page = $request->input('page', 1);
        $search = $request->input('search');

        $query = ResultImport::query();

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                // Search in import_data JSON field for StudentId or UnitId
                $q->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(import_data, '$.StudentId')) LIKE ?", ["%{$search}%"])
                  ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(import_data, '$.UnitId')) LIKE ?", ["%{$search}%"]);
            });
        }

        // Define allowed sortable fields to prevent SQL injection
        $allowedSortFields = ['id', 'status', 'created_at', 'updated_at'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }

        $imports = $query->orderBy($sortBy, $sortDirection)
            ->paginate($perPage, ['*'], 'page', $page);

        // Always return an Inertia response for Inertia requests
        return Inertia::render('result-import/ResultImportMain', [
            'grid' => $imports,
            'data' => [
                'imports' => $imports,
            ],
        ]);
    }

    /**
     * Store a newly created result import.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,txt',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 'errors', $validator->errors(), 422);
        }

        $file = $request->file('file');
        $importBatchId = uniqid('Import_');
        $imports = [];

        try {
            $path = $file->getRealPath();
            $handle = fopen($path, 'r');

            // Read the header row
            $headers = fgetcsv($handle);

            // Process each row
            while (($row = fgetcsv($handle)) !== false) {
                // Combine headers with row values
                $rawData = array_combine($headers, $row);

                // Prepare the data structure with fixed fields
                $data = [];

                // Extract fixed fields
                $fixedFields = ['StudentId', 'CourseType', 'UnitId', 'TotalMark', 'FinalOutcome'];
                foreach ($fixedFields as $field) {
                    $data[$field] = $rawData[$field] ?? null;
                }

                // Store all assessment data in assessmentData key
                $assessmentData = [];
                foreach ($rawData as $key => $value) {
                    // If the key is not one of the fixed fields, consider it as assessment data
                    if (! in_array($key, $fixedFields) && ! empty($value)) {
                        $assessmentData[$key] = $value;
                    }
                }

                // Add assessment data to the main data structure
                if (! empty($assessmentData)) {
                    $data['assessmentData'] = $assessmentData;
                }

                // Create import record with the structured data
                $import = ResultImport::create([
                    'import_data' => $data,
                    'status' => 'pending',
                    'import_batch_id' => $importBatchId,
                    'created_by' => Auth::id(),
                    'updated_by' => Auth::id(),
                ]);

                // Dispatch job for processing
                ProcessResultImport::dispatch($import);

                $imports[] = $import;
            }

            fclose($handle);

            return $this->successResponse(
                'CSV imported successfully! Records are being processed in the background.',
                'imports',
                $imports
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Error importing CSV: '.$e->getMessage(),
                'error',
                null,
                500
            );
        }
    }

    /**
     * Display the specified result import.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $import = ResultImport::findOrFail($id);

        return $this->successResponse('Import details retrieved successfully', 'data', $import);
    }

    /**
     * Update the specified result import.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $import = ResultImport::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'import_data' => 'required|array',
            'import_data.StudentId' => 'required',
            'import_data.UnitId' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 'errors', $validator->errors(), 422);
        }

        // Get the import data from the request
        $importData = $request->input('import_data');

        // Ensure the structure is correct
        $fixedFields = ['StudentId', 'CourseType', 'UnitId', 'TotalMark', 'FinalOutcome'];
        $data = [];

        // Extract fixed fields
        foreach ($fixedFields as $field) {
            $data[$field] = $importData[$field] ?? null;
        }

        // Handle assessment data if present
        if (isset($importData['assessmentData']) && is_array($importData['assessmentData'])) {
            $data['assessmentData'] = $importData['assessmentData'];
        }

        $import->update([
            'import_data' => $data,
            'status' => 'pending',
            'error_message' => null,
            'updated_by' => Auth::id(),
        ]);

        // Reprocess the import
        ProcessResultImport::dispatch($import);

        return $this->successResponse(
            'Import updated and queued for reprocessing!',
            'import',
            $import
        );
    }

    /**
     * Remove the specified result import.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $import = ResultImport::findOrFail($id);
        $import->delete();

        return $this->successResponse('Import deleted successfully!', 'data', null);
    }

    /**
     * Resync a failed import.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resync($id)
    {
        $import = ResultImport::findOrFail($id);

        // Reset status to pending
        $import->update([
            'status' => 'pending',
            'error_message' => null,
            'updated_by' => Auth::id(),
        ]);

        // Dispatch job for reprocessing
        ProcessResultImport::dispatch($import);

        return $this->successResponse(
            'Import queued for reprocessing!',
            'import',
            $import
        );
    }

    /**
     * Download a sample CSV file.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadSample($type)
    {
        $sampleData = [];
        if ($type == 'highered') {
            $headers = [
                'StudentId', 'CourseType', 'UnitId', 'TotalMark',
                'Assessment_1', 'Assessment_2', 'Assessment_3',
            ];

            $sampleData = [
                ['STU001', 'HigherEd', 'SUB001', '85', '28', '27', '30'],
                ['STU002', 'HigherEd', 'SUB002', '75', '25', '20', '30'],
            ];
        } else {
            $headers = [
                'StudentId', 'CourseType', 'UnitId', 'FinalOutcome',
                'Assessment_1', 'Assessment_2', 'Assessment_3',
            ];

            $sampleData = [
                ['STU001', 'VET', 'SUB001', 'C', 'S', 'S', 'S'],
                ['STU002', 'VET', 'SUB002', 'NYC', 'S', 'S', 'NYS'],
            ];
        }

        $filename = "result_import_sample_{$type}.csv";
        $path = Config::get('constants.uploadFilePath.TempFiles');
        $path = Helpers::changeRootPath($path)['default'].$filename;

        $file = fopen($path, 'w');
        fputcsv($file, $headers);

        foreach ($sampleData as $row) {
            fputcsv($file, $row);
        }

        fclose($file);

        return response()->download($path, $filename, [
            'Content-Type' => 'text/csv',
        ])->deleteFileAfterSend();
    }
}
